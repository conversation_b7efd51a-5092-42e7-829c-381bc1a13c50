"use client";

import { cn } from "@/lib/utils";
import { routeList } from "@/lib/route-list";
import { Button } from "@/components/ui/button";
import {
	Toolt<PERSON>,
	TooltipTrigger,
	TooltipContent,
	TooltipProvider,
} from "@/components/ui/tooltip";
import { Link } from "@tanstack/react-router";
import { SheetClose } from "@/components/ui/sheet";
import { useAuthStore } from "@/features/auth/store";
import { useShallow } from "zustand/react/shallow";
import { Bell } from "react-feather";

interface MenuProps {
	isOpen: boolean | undefined;
}

// Mobile-specific menu items (includes Notifications for mobile only)
const mobileUserMenus = [
	{ href: "", label: "Notifications", icon: Bell, submenus: [] },
];

export function MobileMenu({ isOpen }: MenuProps) {
	const { logout } = useAuthStore(
		useShallow((state) => ({
			logout: state.logout,
		}))
	);
	const pathname = window.location.pathname;
	const menuList = routeList.filter((menu) => !menu.exclude);

	const handleClick = (e: React.MouseEvent<HTMLButtonElement>) => {
		const value = e.currentTarget.value;
		switch (value) {
			case "Logout":
				logout();
				break;
			default:
				break;
		}
	};

	const isComingSoon = (label: string) => {
		return label !== "Mock Test" && label !== "Dashboard" && label !== "Analytics" && label !== "Logout";
	};

	return (
		<nav className="flex flex-col w-full">
			<ul className="flex border-y border-y-[#E2E8F01A] grow flex-col items-start space-y-2 px-2 py-6">
				{menuList.map(({ groupLabel, menus }, groupIndex) => (
					<li
						key={groupIndex}
						className={cn(
							"flex w-full flex-col gap-y-2",
							groupLabel ? "pt-5" : groupIndex !== 0 && "border-t"
						)}
					>
						{groupLabel && (
							<p className="text-sm font-medium text-muted-foreground px-4 pb-2 max-w-[248px] truncate">
								{groupLabel}
							</p>
						)}
						{menus.map(({ href, label, icon: Icon, active }, index) => (
							<div className="w-full relative" key={index}>
								<TooltipProvider disableHoverableContent>
									<Tooltip delayDuration={100}>
										<TooltipTrigger asChild>
											<SheetClose asChild>
												<Button
													variant={
														(active === undefined &&
															href.length > 0 &&
															pathname.startsWith(href) &&
															!isComingSoon(label)) ||
														active
															? "active"
															: "inactive"
													}
													className={cn(
														"rounded-full text-base w-full justify-start gap-3 h-11",
														isComingSoon(label) &&
															"opacity-70 cursor-not-allowed"
													)}
													value={label}
													onClick={
														isComingSoon(label) ? undefined : handleClick
													}
													asChild={href.length > 0 && !isComingSoon(label)}
												>
													{href.length > 0 && !isComingSoon(label) ? (
														<Link to={href}>
															<span className={cn(isOpen === false ? "" : "mr-0")}>
																<Icon size={24} />
															</span>
															<p
																className={cn(
																	"max-w-[200px] truncate",
																	isOpen === false
																		? "-translate-x-96 opacity-0"
																		: "translate-x-0 opacity-100"
																)}
															>
																{label}
															</p>
														</Link>
													) : (
														<>
															<span className={cn(isOpen === false ? "" : "mr-0")}>
																<Icon size={24} />
															</span>
															<p
																className={cn(
																	"max-w-[200px] truncate",
																	isOpen === false
																		? "-translate-x-96 opacity-0"
																		: "translate-x-0 opacity-100"
																)}
															>
																{label}
															</p>
														</>
													)}
												</Button>
											</SheetClose>
										</TooltipTrigger>
										{isOpen === false && (
											<TooltipContent side="right">{label}</TooltipContent>
										)}
									</Tooltip>
								</TooltipProvider>
							</div>
						))}
					</li>
				))}
				{/* Mobile-only user menu items */}
				<li className="flex w-full flex-col gap-y-2 border-t pt-2">
					{mobileUserMenus.map(({ href, label, icon: Icon }, index) => (
						<div className="w-full relative" key={index}>
							<TooltipProvider disableHoverableContent>
								<Tooltip delayDuration={100}>
									<TooltipTrigger asChild>
										<SheetClose asChild>
											<Button
												className={cn(
													"rounded-full text-base w-full justify-start gap-3 h-11"
												)}
												asChild={href.length > 0}
												value={label}
												onClick={handleClick}
											>
												{href.length > 0 ? (
													<Link to={href}>
														<span className={cn(isOpen === false ? "" : "mr-0")}>
															<Icon size={24} />
														</span>
														<p
															className={cn(
																"max-w-[200px] truncate",
																isOpen === false
																	? "-translate-x-96 opacity-0"
																	: "translate-x-0 opacity-100"
															)}
														>
															{label}
														</p>
													</Link>
												) : (
													<>
														<span className={cn(isOpen === false ? "" : "mr-0")}>
															<Icon size={24} />
														</span>
														<p
															className={cn(
																"max-w-[200px] truncate",
																isOpen === false
																	? "-translate-x-96 opacity-0"
																	: "translate-x-0 opacity-100"
															)}
														>
															{label}
														</p>
													</>
												)}
											</Button>
										</SheetClose>
									</TooltipTrigger>
									{isOpen === false && (
										<TooltipContent side="right">{label}</TooltipContent>
									)}
								</Tooltip>
							</TooltipProvider>
						</div>
					))}
				</li>
			</ul>
		</nav>
	);
}
